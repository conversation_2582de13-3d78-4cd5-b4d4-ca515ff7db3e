<script lang="ts">
	import { createEventDispatcher, onMount, afterUpdate, tick } from 'svelte';
	import { formatMessageTime, formatMessageDate } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import {
		ChevronDownOutline,
		ChevronRightOutline,
		MailBoxOutline,
		ClockOutline,
		ReplyOutline,
		ReplyAllOutline,
		ForwardOutline
	} from 'flowbite-svelte-icons';
	import EmailCompositionModal from './EmailCompositionModal.svelte';

	// Email thread interface
	interface EmailMessage {
		id: number;
		threadId: string;
		sender: {
			name: string;
			email: string;
			avatar?: string;
		};
		subject: string;
		body: string;
		timestamp: string;
		isRead: boolean;
		isFromSelf: boolean;
		hasAttachments?: boolean;
		attachmentCount?: number;
	}

	interface EmailThread {
		id: string;
		subject: string;
		participants: string[];
		messageCount: number;
		lastActivity: string;
		isExpanded: boolean;
		messages: EmailMessage[];
		hasUnread: boolean;
		isNew?: boolean;
	}

	// Props
	export let threads: EmailThread[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	// Reference to the scrollable container
	let scrollContainer: HTMLDivElement;

	// Modal composition state management
	interface CompositionData {
		action: 'reply' | 'replyAll' | 'forward' | 'compose';
		originalMessageId?: number;
		content: string;
		subject: string;
		to: string[];
		cc: string[];
		bcc: string[];
		originalMessage?: {
			sender: { name: string; email: string };
			timestamp: string;
			body: string;
		};
	}

	// Modal state
	let isCompositionModalOpen: boolean = false;
	let currentCompositionData: CompositionData | null = null;

	// Mock data for initial development - organized by different days
	const mockThreads: EmailThread[] = [
		// TODAY - New thread with comprehensive conversation
		{
			id: 'thread-1',
			subject: 'New Chilli Piper Licenses - Implementation Timeline',
			participants: ['Samuel Jennings', 'You', 'IT Team'],
			messageCount: 6,
			lastActivity: '2025-09-27T14:31:00Z',
			isExpanded: false,
			hasUnread: true,
			isNew: true,
			messages: [
				{
					id: 1,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
					},
					subject: 'New Chilli Piper Licenses - Implementation Timeline',
					body: 'Hi Scott,\n\nI hope this email finds you well. We need to discuss the implementation timeline for the new Chilli Piper licenses we discussed last week.\n\nOur sales team is growing rapidly and we need to onboard 15 new users by the end of Q4. Can you provide an estimate on when we can expect the licenses to be provisioned?\n\nAdditionally, I\'d like to schedule a brief call to discuss the training requirements for the new users.\n\nBest regards,\nSamuel',
					timestamp: '2025-09-25T09:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				},
				{
					id: 2,
					threadId: 'thread-1',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: New Chilli Piper Licenses - Implementation Timeline',
					body: 'Hi Samuel,\n\nThanks for reaching out. I\'ve reviewed our current license allocation and capacity.\n\nFor the 15 new licenses:\n- Provisioning: 3-5 business days from approval\n- Setup and configuration: 2 business days\n- User training sessions: We can schedule these for the following week\n\nI\'ll need the list of new users with their email addresses and role requirements. Can you send that over?\n\nRegarding the call, I\'m available Tuesday or Wednesday afternoon. Let me know what works best for you.\n\nBest,\nScott',
					timestamp: '2025-09-25T14:22:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 3,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
					},
					subject: 'Re: New Chilli Piper Licenses - Implementation Timeline',
					body: 'Perfect! That timeline works well for us.\n\nI\'ll send over the user list by end of day today. Most of them will need standard sales rep access, but 3 will need admin privileges for team management.\n\nLet\'s schedule the call for Wednesday at 2 PM. I\'ll send a calendar invite shortly.\n\nThanks for the quick turnaround on this!\n\nSamuel',
					timestamp: '2025-09-25T16:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 4,
					threadId: 'thread-1',
					sender: {
						name: 'IT Team',
						email: '<EMAIL>',
					},
					subject: 'Re: New Chilli Piper Licenses - Implementation Timeline',
					body: 'Hi Scott and Samuel,\n\nI\'ve been looped into this thread regarding the Chilli Piper license expansion.\n\nFrom an IT perspective, we\'ll need to:\n1. Update our SSO configuration for the new users\n2. Ensure proper security groups are configured\n3. Set up the necessary integrations with our CRM\n\nThis will add approximately 1-2 days to the timeline. I recommend we start the SSO updates as soon as we receive the user list.\n\nPlease include me in Wednesday\'s call so we can coordinate the technical aspects.\n\nBest,\nIT Support Team',
					timestamp: '2025-09-26T10:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 5,
					threadId: 'thread-1',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: New Chilli Piper Licenses - Implementation Timeline',
					body: 'Thanks for jumping in, IT Team.\n\nGood point about the SSO configuration. Let\'s update our timeline:\n\nDay 1-2: User list review and license provisioning\nDay 3-4: SSO configuration and security group setup\nDay 5-6: CRM integration and testing\nDay 7: User training and go-live\n\nSamuel, this pushes our timeline to about 7 business days total. Does this still work with your Q4 goals?\n\nI\'ll add IT to Wednesday\'s call. Looking forward to getting this project moving!\n\nScott',
					timestamp: '2025-09-26T13:15:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 6,
					threadId: 'thread-1',
					sender: {
						name: 'Samuel Jennings',
						email: '<EMAIL>',
					},
					subject: 'Re: New Chilli Piper Licenses - Implementation Timeline',
					body: 'Scott, IT Team,\n\nThe 7-day timeline is absolutely perfect! This gives us plenty of buffer before our Q4 push.\n\nI\'ve attached the complete user list with role requirements and current email addresses. You\'ll notice I\'ve also included their current CRM access levels for reference.\n\nLooking forward to our call on Wednesday. This is going to make a huge difference for our sales efficiency.\n\nThanks to both teams for the excellent coordination!\n\nBest,\nSamuel\n\nP.S. - I\'ve also included some initial training materials that might be helpful.',
					timestamp: '2025-09-27T14:31:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				}
			]
		},
		// TODAY - Budget review thread with multiple stakeholders
		{
			id: 'thread-2',
			subject: 'Q4 Budget Review Meeting - Department Allocations',
			participants: ['Finance Team', 'You', 'Sarah Chen', 'Mike Rodriguez', 'Department Heads'],
			messageCount: 5,
			lastActivity: '2025-09-27T11:45:00Z',
			isExpanded: false,
			hasUnread: true,
			messages: [
				{
					id: 7,
					threadId: 'thread-2',
					sender: {
						name: 'Finance Team',
						email: '<EMAIL>',
					},
					subject: 'Q4 Budget Review Meeting - Department Allocations',
					body: 'Hi everyone,\n\nWe need to schedule our Q4 budget review meeting to finalize department allocations for the upcoming quarter.\n\nKey agenda items:\n• Review Q3 spending vs. budget\n• Discuss Q4 forecasts and adjustments\n• Approve additional headcount requests\n• Technology and infrastructure investments\n\nPlease let me know your availability for next week. I\'m proposing either Tuesday 10 AM or Thursday 2 PM.\n\nI\'ve attached the preliminary budget analysis for your review.\n\nBest regards,\nFinance Team',
					timestamp: '2025-09-26T09:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				},
				{
					id: 8,
					threadId: 'thread-2',
					sender: {
						name: 'Sarah Chen',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Budget Review Meeting - Department Allocations',
					body: 'Thanks for organizing this.\n\nTuesday 10 AM works perfectly for me. I\'ve reviewed the preliminary analysis and have a few questions about the marketing budget allocation.\n\nCould we also discuss the proposed increase in digital advertising spend? I think there\'s an opportunity to reallocate some traditional marketing budget to digital channels for better ROI.\n\nI\'ll prepare a brief presentation with supporting data.\n\nSarah',
					timestamp: '2025-09-26T14:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 9,
					threadId: 'thread-2',
					sender: {
						name: 'Mike Rodriguez',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Budget Review Meeting - Department Allocations',
					body: 'Tuesday works for me as well.\n\nFrom an operations perspective, we\'re going to need to discuss the warehouse expansion budget. Our current capacity is at 85% and we\'re projecting 95% by end of Q4.\n\nAlso, the equipment maintenance budget needs a 15% increase due to aging machinery. I\'ve got quotes from three vendors that I can share during the meeting.\n\nLet me know if you need any additional documentation beforehand.\n\nMike',
					timestamp: '2025-09-26T16:22:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 10,
					threadId: 'thread-2',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: Q4 Budget Review Meeting - Department Allocations',
					body: 'Great discussion points from everyone.\n\nTuesday 10 AM is confirmed on my calendar. I\'ll book the large conference room and set up the projector.\n\nA few items from my end:\n• IT infrastructure upgrades are critical - our current servers are running at capacity\n• We need to budget for the new CRM implementation we discussed\n• Cybersecurity audit and compliance costs should be factored in\n\nI\'ll compile a comprehensive IT budget proposal and circulate it before the meeting.\n\nLooking forward to a productive discussion!\n\nBest,\nScott',
					timestamp: '2025-09-27T08:30:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 11,
					threadId: 'thread-2',
					sender: {
						name: 'Finance Team',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Budget Review Meeting - Department Allocations',
					body: 'Perfect! Tuesday 10 AM is confirmed.\n\nThank you all for the preliminary input. This will help us have a much more focused and productive meeting.\n\nI\'ve updated the agenda to include:\n• Sarah\'s digital marketing reallocation proposal\n• Mike\'s warehouse expansion and equipment maintenance needs\n• Scott\'s IT infrastructure and CRM implementation budget\n\nI\'ll send out the updated budget spreadsheet with your preliminary requests incorporated by Monday evening.\n\nSee you all Tuesday!\n\nFinance Team',
					timestamp: '2025-09-27T11:45:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				}
			]
		},
		// YESTERDAY - Development team standup with action items
		{
			id: 'thread-3',
			subject: 'Weekly Team Standup Notes - Sprint 23 Progress',
			participants: ['Alex Thompson', 'Development Team', 'You', 'QA Team', 'Product Manager'],
			messageCount: 4,
			lastActivity: '2025-09-26T16:20:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 12,
					threadId: 'thread-3',
					sender: {
						name: 'Alex Thompson',
						email: '<EMAIL>',
					},
					subject: 'Weekly Team Standup Notes - Sprint 23 Progress',
					body: 'Hi team,\n\nHere are the notes from our weekly standup meeting. Please review and let me know if I missed anything important.\n\n**Sprint 23 Progress Summary:**\n\n**Completed This Week:**\n• User authentication refactor (Sarah)\n• API rate limiting implementation (Mike)\n• Database optimization for reports (David)\n• Mobile responsive fixes for dashboard (Lisa)\n\n**In Progress:**\n• Payment gateway integration (Tom) - 70% complete\n• Email notification system (Sarah) - Testing phase\n• Performance monitoring dashboard (Mike) - UI pending\n\n**Blockers:**\n• Waiting for third-party API documentation (Tom)\n• QA environment needs database refresh (Lisa)\n\n**Next Week\'s Focus:**\n• Complete payment gateway integration\n• Begin user onboarding flow redesign\n• Address performance issues in reports module\n\nPlease update your task statuses in Jira by EOD Friday.\n\nBest,\nAlex',
					timestamp: '2025-09-26T10:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 3
				},
				{
					id: 13,
					threadId: 'thread-3',
					sender: {
						name: 'Sarah Kim',
						email: '<EMAIL>',
					},
					subject: 'Re: Weekly Team Standup Notes - Sprint 23 Progress',
					body: 'Thanks Alex!\n\nJust a quick update on the email notification system - I found a bug in the template rendering that was causing some emails to display incorrectly. I\'ve fixed it and pushed the changes to the testing branch.\n\nThe QA team can start testing the email flows tomorrow morning. I\'ve prepared test scenarios for:\n• Welcome emails\n• Password reset notifications\n• Account verification\n• Weekly digest emails\n\nLet me know if you need anything else!\n\nSarah',
					timestamp: '2025-09-26T12:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 14,
					threadId: 'thread-3',
					sender: {
						name: 'Tom Wilson',
						email: '<EMAIL>',
					},
					subject: 'Re: Weekly Team Standup Notes - Sprint 23 Progress',
					body: 'Hey team,\n\nUpdate on the payment gateway integration:\n\nGood news - I finally received the API documentation from the vendor! I\'ve reviewed it and the integration is more straightforward than expected.\n\nI should be able to complete the core functionality by Wednesday and have it ready for testing by Thursday.\n\nOne thing to note: they require webhook endpoints for payment confirmations. I\'ll need to coordinate with DevOps to set up the proper SSL certificates for the staging environment.\n\nCan someone from DevOps reach out to me about this?\n\nThanks,\nTom',
					timestamp: '2025-09-26T14:20:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 15,
					threadId: 'thread-3',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: Weekly Team Standup Notes - Sprint 23 Progress',
					body: 'Great progress everyone!\n\nTom, I\'ll handle the SSL certificate setup for the webhook endpoints. I\'ll have the staging environment ready by Tuesday morning.\n\nRegarding the QA database refresh that Lisa mentioned - I\'ve scheduled it for tonight at 11 PM. The environment should be ready for testing first thing tomorrow.\n\nAlso, I\'ve noticed some performance issues with the reports module during peak hours. I\'ll investigate and provide recommendations for optimization.\n\nKeep up the excellent work team!\n\nScott',
					timestamp: '2025-09-26T16:20:00Z',
					isRead: true,
					isFromSelf: true
				}
			]
		},
		// YESTERDAY - Client feedback thread with detailed requirements
		{
			id: 'thread-4',
			subject: 'Client Feedback on Latest Prototype - Revision Requests',
			participants: ['Jennifer Martinez', 'You', 'Design Team', 'Product Manager', 'Client - Acme Corp'],
			messageCount: 6,
			lastActivity: '2025-09-26T14:15:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 16,
					threadId: 'thread-4',
					sender: {
						name: 'Jennifer Martinez',
						email: '<EMAIL>',
					},
					subject: 'Client Feedback on Latest Prototype - Revision Requests',
					body: 'Hi team,\n\nI just finished the prototype review call with Acme Corp. Overall, they are very pleased with the direction and functionality we\'ve delivered.\n\n**Positive Feedback:**\n• Love the new dashboard layout and data visualization\n• User interface is intuitive and modern\n• Performance improvements are noticeable\n• Mobile responsiveness exceeds expectations\n\n**Requested Changes:**\n• Color scheme adjustment - they prefer their brand colors (blue/green palette)\n• Add export functionality to all reports\n• Implement role-based permissions for different user types\n• Include a "quick actions" sidebar for common tasks\n\n**Priority Items:**\n• Export functionality is critical for their Q4 reporting\n• Role-based permissions needed before user training begins\n\nI\'ve attached their detailed feedback document and brand guidelines.\n\nCan we schedule a team meeting to discuss timeline and resource allocation?\n\nBest,\nJennifer',
					timestamp: '2025-09-25T11:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				},
				{
					id: 17,
					threadId: 'thread-4',
					sender: {
						name: 'Design Team',
						email: '<EMAIL>',
					},
					subject: 'Re: Client Feedback on Latest Prototype - Revision Requests',
					body: 'Thanks Jennifer!\n\nGreat feedback from the client. I\'ve reviewed their brand guidelines and the color scheme changes are straightforward.\n\n**Design Timeline:**\n• Color scheme update: 2 days\n• Quick actions sidebar design: 3 days\n• Role-based UI variations: 4 days\n\nThe brand colors will actually work better with our current design system. I\'ll start on the color updates immediately and have mockups ready by Thursday.\n\nFor the quick actions sidebar, I\'d like to understand their most common workflows better. Can we get a list of their top 10 most-used features?\n\nI\'m available for a team meeting anytime this week.\n\nBest,\nDesign Team',
					timestamp: '2025-09-25T14:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 18,
					threadId: 'thread-4',
					sender: {
						name: 'Product Manager',
						email: '<EMAIL>',
					},
					subject: 'Re: Client Feedback on Latest Prototype - Revision Requests',
					body: 'Excellent work on the client presentation, Jennifer!\n\nFrom a product perspective, these changes align well with our roadmap:\n\n**Export Functionality:**\n• We already have the backend infrastructure\n• Frontend implementation: 5-7 days\n• Testing and QA: 2-3 days\n\n**Role-Based Permissions:**\n• This is more complex and will require database schema updates\n• Estimated timeline: 10-12 days\n• We should prioritize this given their training schedule\n\nI suggest we tackle the export functionality first since it\'s quicker to implement and addresses their Q4 reporting needs.\n\nLet\'s schedule the team meeting for Friday at 2 PM. I\'ll prepare a detailed project plan with milestones.\n\nBest,\nProduct Team',
					timestamp: '2025-09-25T16:20:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 19,
					threadId: 'thread-4',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: Client Feedback on Latest Prototype - Revision Requests',
					body: 'Great feedback and analysis from everyone!\n\nFrom a technical implementation standpoint:\n\n**Export Functionality:**\n• I can have the API endpoints ready in 3 days\n• Will support PDF, Excel, and CSV formats\n• Includes data filtering and custom date ranges\n\n**Role-Based Permissions:**\n• Database schema changes are straightforward\n• Will implement using our existing auth framework\n• Can leverage JWT tokens for role management\n\n**Infrastructure Considerations:**\n• May need to increase server capacity for export processing\n• Should implement rate limiting for large exports\n• Need to set up automated backups before schema changes\n\nFriday 2 PM works for me. I\'ll prepare technical specifications and resource requirements.\n\nThis is going to be a great enhancement for Acme Corp!\n\nScott',
					timestamp: '2025-09-26T09:15:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 20,
					threadId: 'thread-4',
					sender: {
						name: 'Client - Acme Corp',
						email: '<EMAIL>',
					},
					subject: 'Re: Client Feedback on Latest Prototype - Revision Requests',
					body: 'Hello team,\n\nI wanted to follow up on our prototype review and express how impressed we are with the progress.\n\nThe functionality you\'ve built exceeds our initial expectations, and the user experience is exactly what we envisioned for our team.\n\n**Additional Context:**\nRegarding the quick actions sidebar, our users most frequently:\n1. Generate monthly reports\n2. Export customer data\n3. Update project statuses\n4. Send client notifications\n5. Access team calendars\n\nWe\'re excited about the export functionality timeline. Having this ready for Q4 will be crucial for our year-end reporting to stakeholders.\n\nPlease keep me updated on the development progress. We\'re planning to begin user training in 3 weeks.\n\nThank you for the excellent partnership!\n\nBest regards,\nMichael Chen\nProject Director, Acme Corp',
					timestamp: '2025-09-26T12:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 21,
					threadId: 'thread-4',
					sender: {
						name: 'Jennifer Martinez',
						email: '<EMAIL>',
					},
					subject: 'Re: Client Feedback on Latest Prototype - Revision Requests',
					body: 'Michael,\n\nThank you for the additional context and kind words! It\'s wonderful to hear that we\'re exceeding expectations.\n\nThe quick actions list you provided is perfect - I\'ll share this with the design team immediately so they can prioritize these features in the sidebar.\n\nBased on our team\'s timeline estimates, we should have all requested features completed well before your user training begins. I\'ll send weekly progress updates to keep you informed.\n\nLooking forward to a successful launch!\n\nBest,\nJennifer\n\nP.S. - I\'ll send you preview links as soon as the color scheme updates are ready.',
					timestamp: '2025-09-26T14:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},
		// EARLIER THIS WEEK - Security compliance thread
		{
			id: 'thread-5',
			subject: 'URGENT: Security Update Required - Compliance Deadline',
			participants: ['IT Security', 'All Staff', 'You', 'Compliance Team'],
			messageCount: 4,
			lastActivity: '2025-09-24T09:30:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 22,
					threadId: 'thread-5',
					sender: {
						name: 'IT Security',
						email: '<EMAIL>',
					},
					subject: 'URGENT: Security Update Required - Compliance Deadline',
					body: '🚨 URGENT SECURITY UPDATE REQUIRED 🚨\n\nDear Team,\n\nWe have identified a critical security vulnerability that requires immediate action from all staff members.\n\n**MANDATORY ACTIONS (Deadline: Friday, September 29th):**\n\n1. **Password Update:**\n   • Change your system password immediately\n   • Use minimum 12 characters with mixed case, numbers, and symbols\n   • Do not reuse any of your last 5 passwords\n\n2. **Two-Factor Authentication:**\n   • Enable 2FA on all company accounts\n   • Use Microsoft Authenticator or Google Authenticator\n   • Backup codes will be provided via secure email\n\n3. **Software Updates:**\n   • Install all pending OS and application updates\n   • Restart your computer after updates complete\n\n**Why This Is Critical:**\nWe detected suspicious login attempts targeting our network. This proactive measure ensures our systems remain secure and compliant with industry standards.\n\n**Need Help?**\nIT Support is available 24/7 this week. Contact us immediately if you encounter any issues.\n\nThis is mandatory for all staff members. Non-compliance will result in temporary account suspension.\n\nThank you for your immediate attention to this matter.\n\nIT Security Team',
					timestamp: '2025-09-23T09:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				},
				{
					id: 23,
					threadId: 'thread-5',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: URGENT: Security Update Required - Compliance Deadline',
					body: 'Security Team,\n\nI\'ve completed all required security updates:\n\n✅ Password updated with 16-character complex password\n✅ 2FA enabled using Microsoft Authenticator\n✅ All system updates installed and computer restarted\n✅ Backup codes saved in secure location\n\nI also took the liberty of updating the security protocols for our development servers and enabled additional monitoring.\n\nIs there anything else needed from the IT department perspective? I can assist other team members with their updates if needed.\n\nBest,\nScott',
					timestamp: '2025-09-23T14:45:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 24,
					threadId: 'thread-5',
					sender: {
						name: 'Compliance Team',
						email: '<EMAIL>',
					},
					subject: 'Re: URGENT: Security Update Required - Compliance Deadline',
					body: 'Thank you Scott for the quick response and proactive measures!\n\nFor compliance tracking, I need to document completion status for all departments:\n\n**Current Completion Status:**\n• IT Department: 100% (Scott completed)\n• Finance: 80% (4 of 5 completed)\n• Sales: 60% (9 of 15 completed)\n• Marketing: 75% (6 of 8 completed)\n• Operations: 45% (5 of 11 completed)\n\n**Reminder:** We need 100% compliance by Friday to maintain our security certification.\n\nI\'ll be sending individual reminders to outstanding team members.\n\nThanks for everyone\'s cooperation!\n\nCompliance Team',
					timestamp: '2025-09-24T08:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 25,
					threadId: 'thread-5',
					sender: {
						name: 'IT Security',
						email: '<EMAIL>',
					},
					subject: 'Re: URGENT: Security Update Required - Compliance Deadline',
					body: 'Excellent work Scott! Your proactive approach is exactly what we need.\n\nI\'m pleased to report that we\'ve now reached 85% overall compliance as of this morning.\n\n**Updated Status:**\n• 127 of 150 employees have completed all requirements\n• 18 employees have partial completion\n• 5 employees have not started (will receive direct calls today)\n\nScott, your offer to help other team members is greatly appreciated. Could you assist the Operations team? They seem to be having the most difficulty with the 2FA setup.\n\nWe\'re on track to meet our Friday deadline. Thank you all for taking this seriously!\n\nIT Security Team',
					timestamp: '2025-09-24T09:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},
		// LAST WEEK - Holiday planning with multiple departments
		{
			id: 'thread-6',
			subject: 'Q4 Holiday Schedule Planning - Coverage Requirements',
			participants: ['HR Department', 'You', 'Team Managers', 'Department Heads'],
			messageCount: 5,
			lastActivity: '2025-09-23T15:45:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 26,
					threadId: 'thread-6',
					sender: {
						name: 'HR Department',
						email: '<EMAIL>',
					},
					subject: 'Q4 Holiday Schedule Planning - Coverage Requirements',
					body: 'Dear Team Managers and Department Heads,\n\nAs we approach Q4, it\'s time to finalize our holiday schedule planning to ensure adequate coverage across all departments.\n\n**Important Dates to Consider:**\n• Thanksgiving Week: November 25-29\n• Winter Holiday Period: December 23 - January 2\n• New Year\'s Day: January 1 (Federal Holiday)\n\n**Planning Requirements:**\n• Each department must maintain minimum 60% staffing during holiday periods\n• Critical systems and customer support must have 24/7 coverage\n• All holiday requests must be submitted by October 15th\n• Backup coverage assignments must be confirmed by October 30th\n\n**Submission Process:**\n1. Use the attached holiday request form\n2. Include preferred dates and backup options\n3. Coordinate with your team for coverage planning\n4. Submit to HR and copy your direct manager\n\n**Special Considerations:**\n• IT Department: On-call rotation required for system maintenance\n• Customer Service: Reduced hours but must maintain support coverage\n• Sales: Q4 is critical period - limited time off approved\n• Finance: Year-end closing requires full staffing through December 31st\n\nPlease review the attached policy document and submit your requests promptly.\n\nBest regards,\nHR Department',
					timestamp: '2025-09-20T10:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				},
				{
					id: 27,
					threadId: 'thread-6',
					sender: {
						name: 'Department Head - Sales',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Holiday Schedule Planning - Coverage Requirements',
					body: 'HR Team,\n\nThanks for the early planning notice. This is crucial for our Q4 success.\n\n**Sales Department Holiday Strategy:**\n\nGiven that Q4 is our biggest quarter, we\'re implementing a modified approach:\n\n• **November:** Normal holiday approvals with 70% minimum staffing\n• **December 1-20:** Limited time off - only emergency/pre-approved requests\n• **December 21-31:** Rotating schedule with 50% staffing minimum\n• **January 2-5:** Catch-up period with full team availability\n\n**Team Coordination:**\n• Senior reps will cover for junior team members\n• Account managers must arrange client coverage before any time off\n• All major deals must have backup point of contact assigned\n\nI\'ll have our detailed coverage plan submitted by October 10th.\n\nWe\'re projecting our strongest Q4 ever, so this coordination is essential!\n\nBest,\nSales Management',
					timestamp: '2025-09-21T09:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 28,
					threadId: 'thread-6',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: Q4 Holiday Schedule Planning - Coverage Requirements',
					body: 'HR Team,\n\nFrom an IT perspective, I\'ve reviewed our holiday coverage requirements:\n\n**IT Department Holiday Plan:**\n\n**System Maintenance Windows:**\n• Thanksgiving Weekend: Major server updates scheduled\n• December 30-31: Year-end system backups and maintenance\n• January 1: Minimal staff (emergency only)\n\n**On-Call Rotation:**\n• 24/7 coverage maintained throughout holiday period\n• Primary and backup engineers assigned for each shift\n• Escalation procedures updated for holiday scenarios\n\n**My Personal Schedule:**\n• Taking December 26-29 off (family commitments)\n• Available for emergency calls during this period\n• Will handle New Year\'s Day on-call shift\n\n**Critical Considerations:**\n• All major deployments must be completed by December 20th\n• Security monitoring will be enhanced during low-staffing periods\n• Backup systems tested and verified before holiday periods\n\nI\'ll submit the formal IT holiday schedule by October 12th, including detailed coverage assignments.\n\nLet me know if you need any additional information!\n\nBest,\nScott',
					timestamp: '2025-09-22T11:30:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 29,
					threadId: 'thread-6',
					sender: {
						name: 'Department Head - Customer Service',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Holiday Schedule Planning - Coverage Requirements',
					body: 'Hi everyone,\n\nCustomer Service will maintain our commitment to excellent support throughout the holidays.\n\n**Holiday Support Schedule:**\n\n**Thanksgiving Week:**\n• Thursday: Reduced hours (9 AM - 3 PM)\n• Friday: Skeleton crew (emergency support only)\n• Weekend: Normal weekend coverage\n\n**December Holiday Period:**\n• December 23: Half day (9 AM - 1 PM)\n• December 24-25: Emergency support only\n• December 26-30: Reduced hours (10 AM - 4 PM)\n• December 31: Half day (9 AM - 1 PM)\n• January 1: Emergency support only\n\n**Staffing Strategy:**\n• Cross-training completed for all critical functions\n• Senior agents will handle escalations during reduced staffing\n• Chat support will have extended automated responses\n• Emergency contact procedures updated for all clients\n\n**Team Requests:**\nI\'ve already received 12 holiday requests from my team of 18. We\'re working on a fair rotation system that ensures coverage while allowing everyone some holiday time.\n\nDetailed schedule will be submitted by October 14th.\n\nBest,\nCustomer Service Management',
					timestamp: '2025-09-23T14:20:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 30,
					threadId: 'thread-6',
					sender: {
						name: 'HR Department',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Holiday Schedule Planning - Coverage Requirements',
					body: 'Excellent planning from all departments!\n\nI\'m impressed with the proactive approach everyone is taking. This level of coordination will ensure smooth operations throughout the holiday period.\n\n**Next Steps:**\n• I\'ll compile all department plans into a master holiday schedule\n• Emergency contact lists will be updated and distributed\n• Holiday policy reminders will be sent to all staff\n• Backup coverage assignments will be confirmed by October 30th\n\n**Additional Reminders:**\n• Unused vacation days: Remember our "use it or lose it" policy\n• Holiday pay: Review the updated holiday pay policy in the employee handbook\n• Remote work: Holiday remote work policies remain in effect\n\n**Appreciation:**\nThank you all for the thoughtful planning. This collaborative approach is what makes our company culture special, especially during the holidays.\n\nI\'ll send out the consolidated schedule by October 25th.\n\nHappy early holidays to everyone!\n\nHR Department',
					timestamp: '2025-09-23T15:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				}
			]
		},

		// LAST WEEK - Forwarded email thread with vendor communication
		{
			id: 'thread-7',
			subject: 'Fwd: Server Hardware Upgrade Proposal - Urgent Response Needed',
			participants: ['Vendor - TechCorp', 'You', 'Finance Team', 'CTO'],
			messageCount: 4,
			lastActivity: '2025-09-22T16:30:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 31,
					threadId: 'thread-7',
					sender: {
						name: 'Vendor - TechCorp',
						email: '<EMAIL>',
					},
					subject: 'Server Hardware Upgrade Proposal - Urgent Response Needed',
					body: 'Dear Scott,\n\nI hope this email finds you well. I\'m reaching out regarding the server hardware upgrade we discussed during our last quarterly review.\n\n**Current Situation:**\nOur monitoring shows your current servers are running at 85-90% capacity during peak hours. This is approaching the critical threshold where performance degradation becomes noticeable.\n\n**Proposed Solution:**\n• 4x Dell PowerEdge R750 servers\n• 128GB RAM per server (upgradeable to 256GB)\n• 2TB NVMe SSD storage per server\n• Redundant power supplies and network connections\n• 3-year warranty with 24/7 support\n\n**Investment Details:**\n• Hardware cost: $48,000\n• Installation and configuration: $8,000\n• Data migration services: $4,000\n• Total project cost: $60,000\n\n**Timeline Urgency:**\nWe have a special Q4 pricing promotion that expires October 31st, offering 15% discount on hardware costs. This would save your company approximately $7,200.\n\nAdditionally, our installation team has availability in November, but December is fully booked due to year-end projects.\n\n**Performance Benefits:**\n• 300% increase in processing capacity\n• 50% reduction in response times\n• Enhanced reliability and uptime\n• Future-proofing for next 3-5 years\n\nI\'d love to schedule a call this week to discuss the proposal in detail and answer any technical questions.\n\nPlease let me know your thoughts and availability.\n\nBest regards,\nMark Stevens\nSenior Solutions Architect\nTechCorp Solutions',
					timestamp: '2025-09-21T10:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 3
				},
				{
					id: 32,
					threadId: 'thread-7',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Fwd: Server Hardware Upgrade Proposal - Urgent Response Needed',
					body: 'Hi Finance Team and CTO,\n\nI\'m forwarding this server upgrade proposal from TechCorp for your review and approval consideration.\n\n**My Technical Assessment:**\n\n**Current Pain Points:**\n• Our servers are indeed running at capacity during peak hours\n• We\'ve had 3 performance incidents in the past month\n• Current hardware is 4 years old and approaching end-of-life\n• Backup and recovery times are becoming problematic\n\n**Proposal Evaluation:**\n✅ Hardware specifications are appropriate for our needs\n✅ Pricing is competitive (I\'ve verified with 2 other vendors)\n✅ TechCorp has been reliable for our current infrastructure\n✅ 15% Q4 discount represents significant savings\n\n**Risk Assessment:**\n• Delaying upgrade could result in system failures during peak business periods\n• Current servers have no warranty coverage after December\n• Data migration complexity is manageable with proper planning\n\n**My Recommendation:**\nI strongly recommend proceeding with this upgrade. The timing aligns well with our Q4 budget discussions, and the discount makes it financially attractive.\n\n**Alternative Options:**\n1. Proceed with full upgrade ($52,800 with discount)\n2. Phase 1: Upgrade 2 servers now, 2 servers in Q1 ($26,400 now)\n3. Delay until Q1 (lose discount, risk system failures)\n\nI\'m available to discuss technical details and answer any questions.\n\nBest,\nScott',
					timestamp: '2025-09-21T14:30:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 33,
					threadId: 'thread-7',
					sender: {
						name: 'CTO',
						email: '<EMAIL>',
					},
					subject: 'Re: Fwd: Server Hardware Upgrade Proposal - Urgent Response Needed',
					body: 'Scott,\n\nExcellent analysis and recommendation. I agree that this upgrade is critical for our infrastructure stability.\n\n**Strategic Considerations:**\n• This aligns with our digital transformation roadmap\n• Enhanced capacity will support our planned product launches in Q1\n• Improved performance will directly impact customer satisfaction\n• The 3-year warranty provides good cost predictability\n\n**Questions for Finance:**\n• Can we accommodate the $52,800 investment in Q4 budget?\n• Would it be better to capitalize this as equipment vs. operational expense?\n• Are there any cash flow considerations for Q4?\n\n**Technical Approval:**\nFrom a technical standpoint, I\'m giving this project my full approval. Scott\'s assessment is thorough and the vendor relationship has been solid.\n\n**Timeline Preference:**\nI prefer the full upgrade approach rather than phased implementation. The operational complexity of managing mixed infrastructure isn\'t worth the cash flow benefit.\n\nFinance team - please advise on budget availability and preferred procurement approach.\n\nBest,\nCTO',
					timestamp: '2025-09-22T09:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 34,
					threadId: 'thread-7',
					sender: {
						name: 'Finance Team',
						email: '<EMAIL>',
					},
					subject: 'Re: Fwd: Server Hardware Upgrade Proposal - Urgent Response Needed',
					body: 'Scott and CTO,\n\nThank you for the comprehensive analysis. I\'ve reviewed the proposal from both technical and financial perspectives.\n\n**Budget Analysis:**\n✅ Q4 IT budget has $65,000 remaining capacity\n✅ This investment fits within our approved capital expenditure limits\n✅ The 15% discount makes this very attractive financially\n✅ 3-year warranty aligns with our asset depreciation schedule\n\n**Financial Recommendation:**\n• Proceed with full upgrade ($52,800 with discount)\n• Capitalize as equipment for tax advantages\n• Use existing vendor relationship to expedite procurement\n• Schedule payment for early November to optimize cash flow\n\n**Procurement Process:**\n1. I\'ll prepare the purchase order today\n2. Legal review of contract terms (standard 2-day process)\n3. Executive approval (should be routine given budget availability)\n4. PO issued by Friday to secure Q4 pricing\n\n**ROI Justification:**\n• Prevents potential downtime costs (estimated $10K+ per incident)\n• Improves productivity through better performance\n• Extends infrastructure lifecycle by 3-5 years\n• Supports revenue growth through enhanced system capacity\n\n**Next Steps:**\nScott, please coordinate with TechCorp on:\n• Final technical specifications confirmation\n• Installation timeline preferences\n• Data migration planning\n\nI\'ll have the paperwork ready for signatures by Thursday.\n\nExcellent work on this proposal!\n\nFinance Team',
					timestamp: '2025-09-22T16:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				}
			]
		},

		// OLDER - Customer escalation thread with multiple departments
		{
			id: 'thread-8',
			subject: 'ESCALATED: Customer Data Export Issue - Premium Client',
			participants: ['Customer Support', 'You', 'Product Team', 'Customer Success', 'Client - Enterprise Corp'],
			messageCount: 7,
			lastActivity: '2025-09-19T17:45:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 35,
					threadId: 'thread-8',
					sender: {
						name: 'Customer Support',
						email: '<EMAIL>',
					},
					subject: 'ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'ESCALATION ALERT - PREMIUM CLIENT ISSUE\n\n**Client:** Enterprise Corp (Premium Tier)\n**Issue:** Data export functionality failing for large datasets\n**Severity:** High - Blocking client\'s monthly reporting\n**Ticket ID:** #CS-2024-0892\n\n**Problem Description:**\nEnterprise Corp is unable to export their monthly sales data (approximately 50,000 records). The export process starts but fails after 2-3 minutes with a timeout error.\n\n**Client Impact:**\n• Monthly board presentation scheduled for tomorrow\n• Critical business reporting is blocked\n• Client expressing frustration with service reliability\n• Potential contract renewal risk\n\n**Troubleshooting Attempted:**\n• Cleared browser cache and cookies\n• Tried different browsers (Chrome, Firefox, Safari)\n• Attempted smaller date ranges (still failing)\n• Verified user permissions (all correct)\n\n**Technical Details:**\n• Error occurs consistently at 2m 45s\n• Browser console shows "Request timeout" error\n• No specific error message displayed to user\n• Issue started 3 days ago (worked fine before)\n\n**Client Quote:**\n"This is extremely frustrating. We\'ve been using your platform for 2 years without issues, and now we can\'t access our own data when we need it most. We need this resolved immediately or we\'ll have to consider alternative solutions."\n\n**Urgency:** This needs immediate technical attention. Client has threatened to escalate to executive level if not resolved by end of business today.\n\nPlease advise on next steps and ETA for resolution.\n\nCustomer Support Team',
					timestamp: '2025-09-18T09:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2
				},
				{
					id: 36,
					threadId: 'thread-8',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Customer Support Team,\n\nI\'m taking immediate action on this escalation. This is clearly a critical issue for our premium client.\n\n**Initial Technical Analysis:**\n\nI\'ve reviewed the server logs and identified the root cause:\n• Database query timeout due to recent data volume growth\n• Export process is hitting our 3-minute server timeout limit\n• Issue coincides with their Q3 data volume increase (50% more records)\n\n**Immediate Actions Taken:**\n1. ✅ Increased server timeout to 10 minutes for export processes\n2. ✅ Optimized the export query with better indexing\n3. ✅ Implemented chunked processing for large datasets\n4. ✅ Added progress indicators for long-running exports\n\n**Testing Results:**\n• Successfully exported 75,000 test records in 4m 30s\n• Process now shows progress to users\n• No timeout errors in test environment\n\n**Deployment Plan:**\n• Deploying fix to production in 30 minutes\n• Will monitor closely during deployment\n• Client can test immediately after deployment\n\n**Long-term Solution:**\n• Implementing background job processing for large exports\n• Adding email notification when export completes\n• Enhanced error handling and user feedback\n\n**ETA:** Fix will be live by 11:00 AM today\n\nI\'ll personally test with the client\'s account once deployed and provide confirmation.\n\nScott',
					timestamp: '2025-09-18T10:30:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 37,
					threadId: 'thread-8',
					sender: {
						name: 'Product Team',
						email: '<EMAIL>',
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Scott and Support Team,\n\nExcellent quick response on the technical fix! From a product perspective, this highlights some important improvements we need to make.\n\n**Product Improvements Needed:**\n\n**Short-term (Next Sprint):**\n• Better error messages for timeout scenarios\n• Progress indicators for all long-running operations\n• Automatic retry mechanism for failed exports\n• User notification system for completed background jobs\n\n**Medium-term (Next Quarter):**\n• Implement proper background job processing\n• Add export scheduling functionality\n• Create export history and download management\n• Enhanced filtering options to reduce dataset sizes\n\n**User Experience Enhancements:**\n• Export preview with estimated time\n• Ability to cancel long-running exports\n• Email notifications for completed exports\n• Better guidance on optimal export parameters\n\n**Client Communication:**\nI recommend we proactively reach out to other enterprise clients to:\n1. Inform them of the improvements\n2. Gather feedback on export functionality\n3. Identify other potential issues before they escalate\n\nThis incident shows we need better monitoring for large dataset operations.\n\nI\'ll prioritize these improvements in our next sprint planning.\n\nProduct Team',
					timestamp: '2025-09-18T12:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 38,
					threadId: 'thread-8',
					sender: {
						name: 'Customer Success',
						email: '<EMAIL>',
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Team,\n\nI\'ve been in direct contact with Enterprise Corp throughout this issue. Here\'s the client relationship perspective:\n\n**Client Sentiment:**\n• Initially very frustrated (understandably)\n• Appreciative of our rapid response and transparency\n• Impressed with Scott\'s technical expertise and communication\n• Relieved that the issue is resolved quickly\n\n**Relationship Impact:**\n• This incident actually strengthened our relationship\n• Client sees our commitment to premium support\n• They\'re interested in discussing additional services\n• Contract renewal concerns have been alleviated\n\n**Client Feedback:**\n"While the issue was frustrating, I\'m impressed with how quickly your team responded and resolved it. Scott\'s technical explanation was clear, and the fix worked perfectly. This is the kind of support we expect from a premium partner."\n\n**Proactive Measures:**\n• Scheduled quarterly technical review with client\n• Offering complimentary data analysis consultation\n• Providing early access to new export features\n• Adding them to our beta testing program for enterprise features\n\n**Account Status:**\n✅ Issue resolved to client satisfaction\n✅ Relationship strengthened through excellent support\n✅ Contract renewal on track\n✅ Potential for account expansion identified\n\nExcellent work by the entire team on this escalation!\n\nCustomer Success Team',
					timestamp: '2025-09-18T15:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 39,
					threadId: 'thread-8',
					sender: {
						name: 'Client - Enterprise Corp',
						email: '<EMAIL>',
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Dear Support Team,\n\nI wanted to personally thank you for the exceptional service you provided in resolving our data export issue.\n\n**What Impressed Us:**\n• Immediate acknowledgment and escalation of the issue\n• Clear, technical communication about the problem and solution\n• Rapid deployment of the fix (under 2 hours!)\n• Proactive follow-up to ensure everything was working\n• Transparent explanation of long-term improvements\n\n**Business Impact:**\nThanks to your quick resolution, we were able to:\n• Complete our monthly board presentation on schedule\n• Deliver critical reports to our stakeholders\n• Maintain confidence in our data management processes\n\n**Moving Forward:**\nThis experience has actually increased our confidence in your platform and support team. We\'re interested in:\n• The quarterly technical reviews you mentioned\n• Early access to new enterprise features\n• Exploring additional modules for our growing team\n\n**Feedback for Your Team:**\nScott\'s technical expertise and communication were outstanding. The Customer Success team\'s proactive outreach was much appreciated. This is exactly the kind of partnership we value.\n\nWe look forward to continuing our successful relationship.\n\nBest regards,\nRobert Chen\nDirector of Operations\nEnterprise Corp',
					timestamp: '2025-09-19T08:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false
				},
				{
					id: 40,
					threadId: 'thread-8',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Robert and team,\n\nThank you for the kind words and positive feedback! It\'s incredibly rewarding to turn a challenging situation into a strengthened partnership.\n\n**Technical Follow-up:**\n\nI\'ve completed the long-term improvements we discussed:\n✅ Background job processing is now live for all large exports\n✅ Email notifications implemented for completed exports\n✅ Enhanced monitoring alerts for potential timeout issues\n✅ Improved error handling with clear user guidance\n\n**Proactive Monitoring:**\n• Set up specific alerts for Enterprise Corp account activity\n• Monitoring export performance across all premium clients\n• Weekly performance reports to identify trends early\n\n**Quarterly Technical Review:**\nI\'m looking forward to our scheduled technical review next month. I\'ll prepare:\n• Performance analysis of your account\n• Recommendations for optimization\n• Preview of upcoming features that might benefit your workflows\n• Discussion of any technical requirements for your growing team\n\n**Personal Note:**\nThis type of collaboration is what makes our work meaningful. Your patience during the issue and constructive feedback afterward helped us improve not just for Enterprise Corp, but for all our clients.\n\nPlease don\'t hesitate to reach out directly if you encounter any technical questions or need assistance.\n\nBest regards,\nScott\n\nP.S. - I\'ve added some additional optimizations specifically for your data patterns that should make future exports even faster!',
					timestamp: '2025-09-19T14:20:00Z',
					isRead: true,
					isFromSelf: true
				},
				{
					id: 41,
					threadId: 'thread-8',
					sender: {
						name: 'Customer Support',
						email: '<EMAIL>',
					},
					subject: 'Re: ESCALATED: Customer Data Export Issue - Premium Client',
					body: 'Team,\n\nWhat an incredible example of how to handle a critical escalation! This case study should be shared across the entire organization.\n\n**Escalation Resolution Summary:**\n\n**Timeline:**\n• Issue reported: 9:15 AM\n• Technical analysis completed: 10:30 AM\n• Fix deployed: 11:00 AM\n• Client confirmation: 11:30 AM\n• Total resolution time: 2 hours 15 minutes\n\n**Key Success Factors:**\n• Immediate escalation and clear communication\n• Technical expertise and rapid problem-solving\n• Proactive client relationship management\n• Long-term improvements implemented\n• Client satisfaction exceeded expectations\n\n**Lessons Learned:**\n• Early detection systems needed for large dataset operations\n• Premium clients need proactive monitoring\n• Technical transparency builds trust\n• Quick resolution can strengthen relationships\n\n**Recognition:**\n• Scott: Outstanding technical leadership and client communication\n• Product Team: Excellent strategic thinking on improvements\n• Customer Success: Masterful relationship management\n• Entire team: Collaborative approach to problem-solving\n\n**Process Improvements:**\nBased on this case, we\'re implementing:\n• Automated alerts for potential timeout scenarios\n• Escalation playbook for premium client issues\n• Regular proactive health checks for enterprise accounts\n• Enhanced monitoring dashboard for support team\n\nThis is exactly the kind of service excellence that sets us apart!\n\nCustomer Support Team',
					timestamp: '2025-09-19T17:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 1
				}
			]
		}
	];

	// Initialize with mock data if no threads provided
	if (threads.length === 0) {
		threads = mockThreads;
	}

	// Track if we've done the initial scroll
	let hasInitialScrolled = false;

	// Toggle thread expansion
	function toggleThread(threadId: string) {
		threads = threads.map(thread => {
			if (thread.id === threadId) {
				return { ...thread, isExpanded: !thread.isExpanded };
			}
			return thread;
		});

		dispatch('threadToggle', { threadId, isExpanded: threads.find(t => t.id === threadId)?.isExpanded });
	}

	// Handle email click
	function handleEmailClick(email: EmailMessage) {
		dispatch('emailClick', { email });
	}

	// Handle email actions
	function handleEmailAction(action: string, email: EmailMessage, event?: Event) {
		if (event) {
			event.stopPropagation();
		}

		// Handle modal composition actions
		if (action === 'reply' || action === 'replyAll' || action === 'forward') {
			startModalComposition(action as 'reply' | 'replyAll' | 'forward', email);
		} else {
			// Dispatch other actions as before
			dispatch('emailAction', { action, email });
		}
	}

	// Start modal composition
	function startModalComposition(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage) {
		currentCompositionData = {
			action,
			originalMessageId: email.id,
			content: getInitialContent(action, email),
			subject: getSubjectForAction(action, email.subject),
			to: getRecipientsForAction(action, email),
			cc: getCCRecipientsForAction(action, email),
			bcc: getBCCRecipientsForAction(action, email),
			originalMessage: {
				sender: email.sender,
				timestamp: email.timestamp,
				body: email.body
			}
		};

		isCompositionModalOpen = true;
	}

	// Get initial content based on action
	function getInitialContent(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string {
		const timestamp = formatMessageTime(email.timestamp);
		const sender = email.sender.name;

		if (action === 'forward') {
			return `<br><br>---------- Forwarded message ----------<br>From: ${sender} &lt;${email.sender.email}&gt;<br>Date: ${timestamp}<br>Subject: ${email.subject}<br><br>${email.body}`;
		} else {
			return `<br><br>On ${timestamp}, ${sender} wrote:<br><blockquote style="margin-left: 20px; border-left: 2px solid #ccc; padding-left: 10px;">${email.body}</blockquote>`;
		}
	}

	// Get subject for action
	function getSubjectForAction(action: 'reply' | 'replyAll' | 'forward', originalSubject: string): string {
		if (action === 'forward') {
			return originalSubject.startsWith('Fwd:') ? originalSubject : `Fwd: ${originalSubject}`;
		} else {
			return originalSubject.startsWith('Re:') ? originalSubject : `Re: ${originalSubject}`;
		}
	}

	// Get recipients for action
	function getRecipientsForAction(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string[] {
		if (action === 'forward') {
			return []; // User will fill in recipients
		} else if (action === 'replyAll') {
			// Include original sender and all participants except current user
			const thread = threads.find(t => t.messages.some(m => m.id === email.id));
			if (thread) {
				return thread.participants.filter(p => p !== 'You');
			}
			return [email.sender.email];
		} else {
			// Reply only to sender
			return [email.sender.email];
		}
	}

	// Get CC recipients for action
	function getCCRecipientsForAction(action: 'reply' | 'replyAll' | 'forward', _email: EmailMessage): string[] {
		if (action === 'replyAll') {
			// For reply all, we might want to include some participants in CC
			// This is a simplified implementation
			return [];
		}
		return [];
	}

	// Get BCC recipients for action
	function getBCCRecipientsForAction(_action: 'reply' | 'replyAll' | 'forward', _email: EmailMessage): string[] {
		// BCC is typically empty for replies and forwards
		// Users can add BCC recipients manually
		return [];
	}

	// Handle modal composition send
	function handleModalSend(event: CustomEvent) {
		// Dispatch the send event with composition data
		dispatch('emailSend', event.detail);

		// Close the modal
		isCompositionModalOpen = false;
		currentCompositionData = null;
	}

	// Handle modal composition cancel/close
	function handleModalCancel() {
		isCompositionModalOpen = false;
		currentCompositionData = null;
	}

	// Start new email composition
	function startNewComposition() {
		currentCompositionData = {
			action: 'compose',
			content: '',
			subject: '',
			to: [],
			cc: [],
			bcc: []
		};

		isCompositionModalOpen = true;
	}

	// Group threads by exact date for display
	function groupThreadsByDate(threads: EmailThread[]) {
		const dateGroups = new Map<string, EmailThread[]>();

		threads.forEach(thread => {
			const threadDate = new Date(thread.lastActivity);
			// Use YYYY-MM-DD format for consistent grouping
			const dateKey = threadDate.toISOString().split('T')[0];

			if (!dateGroups.has(dateKey)) {
				dateGroups.set(dateKey, []);
			}
			dateGroups.get(dateKey)!.push(thread);
		});

		// Convert to array and sort by date (newest first)
		const sortedGroups = Array.from(dateGroups.entries())
			.sort(([dateA], [dateB]) => dateB.localeCompare(dateA))
			.map(([dateKey, threads]) => ({
				dateKey,
				date: new Date(dateKey),
				threads: threads.sort((a, b) =>
					new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
				)
			}));

		return sortedGroups;
	}

	// Get avatar color for sender
	function getAvatarColor(name: string): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];

		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}

		return colors[Math.abs(hash) % colors.length];
	}

	// Format email body with line breaks
	function formatEmailBody(body: string): string {
		if (!body) return '';
		return body.replace(/\n/g, '<br>');
	}

	// Get relative time for last activity
	function getRelativeTime(timestamp: string): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffHours / 24);

		if (diffHours < 1) return 'เมื่อสักครู่';
		if (diffHours < 24) return `${diffHours} ชั่วโมงที่ผ่านมา`;
		if (diffDays === 1) return 'เมื่อวานนี้';
		if (diffDays < 7) return `${diffDays} วันที่ผ่านมา`;

		return formatMessageDate(timestamp);
	}

	// Function to scroll to the latest content (newest threads)
	function scrollToLatestContent() {
		console.log('scrollToLatestContent called, container exists:', !!scrollContainer);

		if (scrollContainer) {
			// Use multiple approaches to ensure scrolling works reliably
			const performScroll = () => {
				// Find the first (newest) date group with content
				const timeGroupElements = scrollContainer.querySelectorAll('[data-time-group]');
				let targetElement = null;

				// Use the first date group (newest date) since they're sorted newest first
				if (timeGroupElements.length > 0) {
					targetElement = timeGroupElements[0];
				}

				if (targetElement) {
					console.log('Scrolling to date group:', targetElement.getAttribute('data-time-group'));
					targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
					return;
				}

				// Fallback: scroll to bottom
				const scrollHeight = scrollContainer.scrollHeight;
				const clientHeight = scrollContainer.clientHeight;
				const currentScrollTop = scrollContainer.scrollTop;

				console.log('Fallback scroll attempt:', {
					scrollHeight,
					clientHeight,
					currentScrollTop,
					needsScroll: scrollHeight > clientHeight,
					maxScrollTop: scrollHeight - clientHeight
				});

				if (scrollHeight > clientHeight) {
					// Try multiple scroll methods
					const targetScrollTop = scrollHeight - clientHeight;
					scrollContainer.scrollTop = targetScrollTop;

					// Verify the scroll worked
					setTimeout(() => {
						const newScrollTop = scrollContainer.scrollTop;
						console.log('Scroll result:', {
							targetScrollTop,
							actualScrollTop: newScrollTop,
							scrollWorked: newScrollTop > currentScrollTop,
							isAtBottom: Math.abs(newScrollTop - targetScrollTop) < 5
						});
					}, 10);
				} else {
					console.log('No scroll needed - content fits in container');
				}
			};

			// Use requestAnimationFrame to ensure DOM is ready
			requestAnimationFrame(() => {
				performScroll();
				// Try again after a short delay in case content is still loading
				setTimeout(performScroll, 100);
			});
		} else {
			console.log('Scroll container not available yet');
		}
	}

	// Auto-scroll to latest content when component mounts
	onMount(async () => {
		console.log('EmailThread component mounted');
		// Wait for DOM to be fully rendered
		await tick();
		// Try scrolling multiple times with different delays to ensure it works
		setTimeout(scrollToLatestContent, 0);
		setTimeout(scrollToLatestContent, 100);
		setTimeout(scrollToLatestContent, 300);
		setTimeout(scrollToLatestContent, 500);
	});

	// Also try scrolling after each update
	afterUpdate(() => {
		if (!hasInitialScrolled && scrollContainer && threads.length > 0) {
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 50);
		}
	});

	// Reactive statement to group threads by date
	$: groupedThreads = groupThreadsByDate(threads);

	// Create date groups with formatted labels
	$: dateGroups = groupedThreads.map(group => ({
		label: formatMessageDate(group.date.toISOString()),
		threads: group.threads,
		dateKey: group.dateKey
	}));

	// Auto-scroll to bottom when threads change (new data loaded) or on initial load
	$: if (threads.length > 0 && scrollContainer && !hasInitialScrolled) {
		// Use tick to ensure DOM is updated before scrolling
		tick().then(() => {
			// Add a small delay to ensure content is fully rendered
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 100);
		});
	}

	// Also scroll when grouped threads change (after content is rendered)
	$: if (groupedThreads && scrollContainer && !hasInitialScrolled) {
		tick().then(() => {
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 150);
		});
	}
</script>

<div class="h-full flex flex-col overflow-hidden">
	{#if loading}
		<div class="flex h-full items-center justify-center">
			<div class="flex items-center space-x-2">
				<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				<span class="text-gray-500">Loading email threads...</span>
			</div>
		</div>
	{:else if threads.length === 0}
		<div class="flex h-full items-center justify-center">
			<div class="text-center text-gray-500">
				<MailBoxOutline class="mx-auto h-12 w-12 mb-4 text-gray-400" />
				<p>No email threads found</p>
			</div>
		</div>
	{:else}
		<!-- Scrollable content area -->
		<div class="flex-1 overflow-y-auto px-4 py-6 relative" bind:this={scrollContainer}>
			<!-- Dynamic date group iteration -->
			{#each dateGroups as dateGroup (dateGroup.dateKey)}
				{#if dateGroup.threads.length > 0}
					<div class="mb-8" data-time-group={dateGroup.label}>
						<div class="flex items-center justify-center mb-8">
							<span class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white">
								{dateGroup.label}
							</span>
						</div>
						{#each dateGroup.threads as thread (thread.id)}
						<div
								class="mb-0 overflow-hidden bg-white border-b border-gray-100 rounded-lg shadow-md"
								data-thread-new={thread.isNew ? 'true' : 'false'}
								data-thread-unread={thread.hasUnread ? 'true' : 'false'}
								data-thread-id={thread.id}
							>
							<!-- Thread header -->
							<div
								class="p-4 cursor-pointer transition-colors duration-150"
								on:click={() => toggleThread(thread.id)}
								on:keydown={(e) => e.key === 'Enter' && toggleThread(thread.id)}
								role="button"
								tabindex="0"
							>
								<div class="flex items-start justify-between">
									<div class="flex items-start space-x-3 flex-1 min-w-0">
										<!-- Expand/collapse icon -->
										<div class="flex-shrink-0 mt-1">
											{#if thread.isExpanded}
												<ChevronDownOutline class="h-4 w-4 text-gray-400" />
											{:else}
												<ChevronRightOutline class="h-4 w-4 text-gray-400" />
											{/if}
										</div>

										<!-- Thread info -->
										<div class="flex-1 min-w-0">
											<div class="flex justify-between items-center space-x-2 mb-1">
												<!-- {#if thread.isNew}
													<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
														NEW
													</span>
												{/if} -->
												<h3 class="text-sm font-medium break-words overflow-wrap-anywhere {thread.hasUnread ? 'text-gray-900' : 'text-gray-500'}">
													{thread.subject}
												</h3>
												<!-- Unread indicator -->
												{#if thread.hasUnread}
													<div class="flex-shrink-0 ml-2">
														<div class="h-2 w-2 bg-red-500 rounded-full"></div>
													</div>
												{/if}
											</div>

											<div class="flex justify-between items-center text-xs text-gray-500">
												<div class="w-64 truncate">
													<span>{thread.participants.join(', ')}</span>
												</div>
												<div>
													<span>{thread.messageCount} ข้อความ</span>
													<!-- <span>•</span> -->
													<!-- <span>{getRelativeTime(thread.lastActivity)}</span> -->
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- Expanded thread messages -->
							{#if thread.isExpanded}
								<div class="thread-messages border-t border-gray-100">
									{#each thread.messages as message (message.id)}
										<div class="border-b border-gray-100 last:border-b-0">
											<div
												class="p-4 cursor-default"
												on:click={() => handleEmailClick(message)}
												on:keydown={(e) => e.key === 'Enter' && handleEmailClick(message)}
												role="button"
												tabindex="0"
											>
												<div class="flex items-start space-x-3">
													<!-- Avatar -->
													<div class="flex-shrink-0">
														{#if message.sender.avatar}
															<img
																src={message.sender.avatar}
																alt={message.sender.name}
																class="h-8 w-8 rounded-full object-cover"
															/>
														{:else}
															<div class="h-8 w-8 rounded-full {getAvatarColor(message.sender.name)} flex items-center justify-center">
																<span class="text-xs font-medium text-white">
																	{getInitials(message.sender.name)}
																</span>
															</div>
														{/if}
													</div>

													<!-- Message content -->
													<div class="flex-1 min-w-0">
														<div class="flex items-center justify-between mb-1">
															<div class="flex items-center space-x-2">
																<span class="text-sm font-medium text-gray-900">
																	{message.sender.name}
																</span>
																{#if !message.isRead}
																	<div class="h-1.5 w-1.5 bg-red-500 rounded-full"></div>
																{/if}
															</div>
															<div class="flex items-center text-xs text-gray-600">
																<!-- Email action buttons -->
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('reply', message, e)}
																>
																	<ReplyOutline class="h-4 w-4" />
																</button>
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('replyAll', message, e)}
																>
																	<ReplyAllOutline class="h-4 w-4" />
																</button>
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('forward', message, e)}
																>
																	<ForwardOutline class="h-4 w-4" />
																</button>
																<div class="flex items-center space-x-2 ml-2">
																	<ClockOutline class="h-4 w-4" />
																	<span>{formatMessageTime(message.timestamp)}</span>
																</div>
															</div>
														</div>

														<p class="text-sm text-gray-600 mb-3 overflow-hidden break-words">
															<!-- {@html formatEmailBody(truncateMessage(message.body, 150))} -->
															{@html formatEmailBody(message.body)}
														</p>

														{#if message.hasAttachments}
															<div class="flex items-center space-x-1 text-xs text-gray-500 mb-3">
																<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
																</svg>
																<span>{message.attachmentCount} เอกสารแนบ</span>
															</div>
														{/if}
													</div>
												</div>
											</div>


										</div>
									{/each}
								</div>
							{/if}
						</div>
					{/each}
				</div>
			{/if}
		{/each}

		<!-- Floating Action Button (FAB) for Compose Email -->
		<button
			class="fab-compose w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-200 z-50 flex items-center justify-center group"
			on:click={() => startNewComposition()}
			title="Compose Email"
			aria-label="Compose Email"
		>
			<svg class="w-6 h-6 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
			</svg>
		</button>
		</div>
	{/if}
</div>


<!-- Email Composition Modal -->
<EmailCompositionModal
	bind:isOpen={isCompositionModalOpen}
	compositionData={currentCompositionData}
	on:send={handleModalSend}
	on:cancel={handleModalCancel}
	on:close={handleModalCancel}
/>
<style>
	/* Custom scrollbar styling for better UX */
	:global(.overflow-y-auto) {
		scrollbar-width: thin;
		scrollbar-color: #cbd5e0 #f7fafc;
	}

	:global(.overflow-y-auto::-webkit-scrollbar) {
		width: 6px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-track) {
		background: #f7fafc;
		border-radius: 3px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-thumb) {
		background: #cbd5e0;
		border-radius: 3px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
		background: #a0aec0;
	}

	/* Ensure proper text wrapping for long content */
	:global(.break-words) {
		word-break: break-word;
		overflow-wrap: break-word;
		hyphens: auto;
	}

	:global(.overflow-wrap-anywhere) {
		overflow-wrap: anywhere;
	}

	/* Floating Action Button (FAB) styles */
	.fab-compose {
		/* Enhanced shadow for better depth perception */
		box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);

		/* Smooth transitions for all interactions */
		transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

		/* Ensure button stays above other content within the container */
		z-index: 100;

		/* Position relative to the scrollable container */
		/* position: absolute; */

		/* Position relative to the wrapper container's visible area */
		position: absolute;
		bottom: 0.8rem;
		right: 1.5rem;
		/* Ensure it stays in place during scroll */
		transform: translateZ(0);
		will-change: transform;
	}

	.fab-compose:hover {
		/* Enhanced hover shadow */
		box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5), 0 4px 8px rgba(0, 0, 0, 0.15);

		/* Slight scale effect on hover */
		transform: scale(1.05);
	}

	.fab-compose:active {
		/* Pressed state */
		transform: scale(0.95);
		box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4), 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	/* Responsive adjustments for smaller screens */
	@media (max-width: 768px) {
		.fab-compose {
			/* Smaller size on mobile */
			width: 3rem;
			height: 3rem;
			bottom: 1rem;
			right: 1rem;
		}

		.fab-compose svg {
			width: 1.25rem;
			height: 1.25rem;
		}
	}

	/* Ensure FAB has proper spacing from container edges on small screens */
	@media (max-width: 640px) {
		.fab-compose {
			bottom: 1rem;
			right: 0.75rem;
		}
	}
</style>
